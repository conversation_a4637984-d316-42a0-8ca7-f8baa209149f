{"environment": {"user": "bigo", "uid": 501, "gid": 20, "groups": ["staff", "access_bpf", "everyone", "localaccounts", "_appserverusr", "admin", "_appserveradm", "_lpadmin", "_appstore", "_lpoperator", "_developer", "_analyticsusers", "com.apple.access_ftp", "com.apple.access_screensharing", "com.apple.access_ssh", "com.apple.access_remote_ae"], "cwd": "/Users/<USER>/thetumbleds-tools/unified-ui", "python_executable": "/Library/Developer/CommandLineTools/usr/bin/python3", "python_path": ["/Users/<USER>/thetumbleds-tools/unified-ui", "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python39.zip", "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9", "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/lib-dynload", "/Users/<USER>/Library/Python/3.9/lib/python/site-packages"], "environment_vars": {"PATH": "/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/usr/local/go/bin:/Users/<USER>/Downloads/apache-maven-3.8.6/bin", "HOME": "/Users/<USER>", "USER": "bigo", "SHELL": "/bin/zsh", "LANG": "NOT_SET", "LC_ALL": "NOT_SET", "PYTHONPATH": "NOT_SET", "PYTHONHOME": "NOT_SET", "VIRTUAL_ENV": "NOT_SET", "GIT_DIR": "NOT_SET", "GIT_WORK_TREE": "NOT_SET", "GIT_CONFIG_GLOBAL": "NOT_SET", "SSH_AUTH_SOCK": "/private/tmp/com.apple.launchd.HfbzCyxEAf/Listeners", "SSH_AGENT_PID": "NOT_SET"}}, "git_access": {"/Users/<USER>/Desktop/testC": {"exists": true, "can_read": true, "can_write": true, "can_execute": true, "git_commands": {"git rev-parse --git-dir": {"returncode": 0, "stdout": ".git\n", "stderr": ""}, "git status --porcelain": {"returncode": 0, "stdout": " M README.md\n?? .idea/codeStyles/\n", "stderr": ""}, "git remote": {"returncode": 0, "stdout": "origin\n", "stderr": ""}}}, "/Users/<USER>/Desktop/pulsar-tlaplus": {"exists": true, "can_read": true, "can_write": true, "can_execute": true, "git_commands": {"git rev-parse --git-dir": {"returncode": 0, "stdout": ".git\n", "stderr": ""}, "git status --porcelain": {"returncode": 0, "stdout": "", "stderr": ""}, "git remote": {"returncode": 0, "stdout": "origin\n", "stderr": ""}}}}, "launch_method": "direct"}